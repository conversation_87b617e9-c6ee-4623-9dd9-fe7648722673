<div>
    @if(empty($cart))
        <!-- Empty Cart -->
        <div class="text-center py-12">
            <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Tu carrito está vacío</h3>
            <p class="text-gray-600 mb-6">Parece que no has añadido ningún producto a tu carrito todavía.</p>
            <a href="{{ route('products.index') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                Continuar Comprando
            </a>
        </div>
    @else
        <!-- Cart Items -->
        <div class="space-y-6 mb-8">
            @foreach($cart as $productId => $item)
                <div class="flex items-center justify-between p-6 bg-gray-50 rounded-lg" wire:key="cart-item-{{ $productId }}">
                    <div class="flex items-center space-x-4 flex-1">
                        <!-- Product Image Placeholder -->
                        <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>

                        <!-- Product Info -->
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $item['nombre'] }}</h3>
                            <p class="text-gray-600">€{{ number_format($item['precio'], 2) }} por unidad</p>
                        </div>
                    </div>

                    <!-- Quantity Controls -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <button wire:click="updateQuantity({{ $productId }}, {{ $item['quantity'] - 1 }})"
                                    wire:loading.attr="disabled"
                                    class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300 disabled:opacity-50">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                </svg>
                            </button>

                            <span class="w-12 text-center font-medium">{{ $item['quantity'] }}</span>

                            <button wire:click="updateQuantity({{ $productId }}, {{ $item['quantity'] + 1 }})"
                                    wire:loading.attr="disabled"
                                    class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300 disabled:opacity-50">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Subtotal -->
                        <div class="text-right min-w-[100px]">
                            <p class="text-lg font-semibold text-gray-900">€{{ number_format($item['subtotal'], 2) }}</p>
                        </div>

                        <!-- Remove Button -->
                        <button wire:click="removeFromCart({{ $productId }})"
                                wire:confirm="¿Estás seguro de que quieres eliminar este producto del carrito?"
                                wire:loading.attr="disabled"
                                class="ml-4 text-red-500 hover:text-red-700 disabled:opacity-50">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Cart Summary -->
        <div class="border-t pt-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <p class="text-sm text-gray-600">{{ $count }} {{ $count === 1 ? 'producto' : 'productos' }} en el carrito</p>
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-gray-900">€{{ number_format($total, 2) }}</p>
                    <p class="text-sm text-gray-600">Total</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row justify-between space-y-4 sm:space-y-0 sm:space-x-4">
                <button wire:click="clearCart"
                        wire:confirm="¿Estás seguro de que quieres vaciar el carrito?"
                        wire:loading.attr="disabled"
                        class="border border-red-600 text-red-600 px-6 py-3 rounded-lg hover:bg-red-50 transition-colors duration-200 disabled:opacity-50">
                    <span wire:loading.remove>Vaciar Carrito</span>
                    <span wire:loading>Vaciando...</span>
                </button>

                <div class="flex space-x-4">
                    <a href="{{ route('products.index') }}"
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        Continuar Comprando
                    </a>

                    <a href="{{ route('checkout.index') }}"
                        class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200">
                        Proceder al Pago
                    </a>
                </div>
            </div>
        </div>
    @endif

    <!-- Loading Indicator -->
    <div wire:loading class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <svg class="animate-spin h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-700">Actualizando carrito...</span>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('error') }}
        </div>
    @endif
</div>
