<?php

namespace App\Http\Controllers;

use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use LBCDev\Ecommerce\Models\Order;
use Illuminate\Http\RedirectResponse;

class CheckoutController extends Controller
{
    /**
     * Show checkout page
     */
    public function index(): View
    {
        // Simply return the view - all logic is now handled by the Livewire component
        return view('checkout.index');
    }

    /**
     * Show success page
     */
    public function success(Request $request): View|RedirectResponse
    {
        $orderId = $request->route('order');
        $order = Order::where('id', $orderId)
            ->where('user_id', Auth::id())
            ->with(['items.product', 'payments'])
            ->first();

        if (!$order) {
            return redirect()->route('home')
                ->with('error', 'Pedido no encontrado.');
        }

        return view('checkout.success', compact('order'));
    }

    /**
     * Show cancel page
     */
    public function cancel(): View
    {
        return view('checkout.cancel');
    }
}
