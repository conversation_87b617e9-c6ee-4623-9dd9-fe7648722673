<?php

namespace App\Livewire;

use LBCDev\Ecommerce\Models\Product;
use LBCDev\Ecommerce\Services\CartService;
use Livewire\Component;

class ShoppingCart extends Component
{
    public $cart = [];
    public $total = 0;
    public $count = 0;
    public $showCart = false;

    protected $listeners = ['cartUpdated' => 'loadCart'];
    protected CartService $cartService;

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount()
    {
        $this->loadCart();
    }

    public function loadCart()
    {
        try {
            $cartModel = $this->cartService->getCart();
            $this->cart = $this->transformCartForView($cartModel);
            $this->calculateTotals();
        } catch (\Exception) {
            $this->cart = [];
            $this->total = 0;
            $this->count = 0;
        }
    }

    private function transformCartForView($cartModel): array
    {
        $cart = [];
        foreach ($cartModel->getItems() as $item) {
            $product = $item->getItem();
            $cart[$product->id] = [
                'id' => $product->id,
                'nombre' => $product->name,
                'precio' => $item->getUnitPrice(),
                'quantity' => $item->getQuantity(),
                'slug' => $product->slug ?? '',
            ];
        }
        return $cart;
    }

    public function addToCart($productId, $quantity = 1)
    {
        try {
            $product = Product::findOrFail($productId);

            if (!$product->is_active) {
                session()->flash('error', 'Producto no disponible.');
                return;
            }

            // Limit quantity per product
            if ($quantity > 10) {
                $quantity = 10;
            }

            $this->cartService->addItem($product, $quantity);
            $this->loadCart();

            session()->flash('success', 'Producto agregado al carrito.');
            $this->dispatch('cartUpdated');
        } catch (\Exception) {
            session()->flash('error', 'Error al agregar el producto al carrito.');
        }
    }

    public function updateQuantity($productId, $quantity)
    {
        try {
            $product = Product::findOrFail($productId);

            if ($quantity <= 0) {
                $this->removeFromCart($productId);
                return;
            }

            if ($quantity > 10) {
                $quantity = 10;
            }

            $this->cartService->updateQuantity($product, $quantity);
            $this->loadCart();

            $this->dispatch('cartUpdated');
        } catch (\Exception) {
            session()->flash('error', 'Error al actualizar la cantidad.');
        }
    }

    public function removeFromCart($productId)
    {
        try {
            $product = Product::findOrFail($productId);
            $this->cartService->removeItem($product);
            $this->loadCart();

            session()->flash('success', 'Producto eliminado del carrito.');
            $this->dispatch('cartUpdated');
        } catch (\Exception) {
            session()->flash('error', 'Error al eliminar el producto del carrito.');
        }
    }

    public function clearCart()
    {
        try {
            $this->cartService->clearCart();
            $this->loadCart();

            session()->flash('success', 'Carrito vaciado.');
            $this->dispatch('cartUpdated');
        } catch (\Exception) {
            session()->flash('error', 'Error al vaciar el carrito.');
        }
    }

    public function toggleCart()
    {
        $this->showCart = !$this->showCart;
    }

    private function calculateTotals()
    {
        $this->total = 0;
        $this->count = 0;

        foreach ($this->cart as $item) {
            $this->total += $item['precio'] * $item['quantity'];
            $this->count += $item['quantity'];
        }

        $this->total = round($this->total, 2);
    }

    public function render()
    {
        return view('livewire.shopping-cart');
    }
}
