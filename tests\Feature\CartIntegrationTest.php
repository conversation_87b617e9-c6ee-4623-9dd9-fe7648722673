<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use LBCDev\Ecommerce\Models\Product;
use App\Livewire\Products\ProductCard;
use App\Livewire\CartIcon;
use App\Livewire\CartPage;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CartIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ejecutar migraciones del paquete de ecommerce
        $this->artisan('migrate', ['--path' => 'vendor/lbcdev/ecommerce/database/migrations']);
    }

    public function test_complete_cart_workflow(): void
    {
        // 1. Crear productos de prueba
        $product1 = Product::factory()->create([
            'name' => 'Curso de Laravel',
            'price' => 49.99,
            'is_active' => true,
        ]);

        $product2 = Product::factory()->create([
            'name' => 'Curso de Vue.js',
            'price' => 39.99,
            'is_active' => true,
        ]);

        // 2. Verificar que el carrito está vacío inicialmente
        Livewire::test(CartIcon::class)
            ->assertSet('count', 0)
            ->assertSet('total', 0);

        // 3. Añadir primer producto al carrito desde ProductCard
        Livewire::test(ProductCard::class, ['product' => $product1])
            ->call('addToCart', 2)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // 4. Verificar que CartIcon se actualiza
        Livewire::test(CartIcon::class)
            ->assertSet('count', 2)
            ->assertSet('total', 99.98); // 49.99 * 2

        // 5. Añadir segundo producto al carrito
        Livewire::test(ProductCard::class, ['product' => $product2])
            ->call('addToCart', 1)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // 6. Verificar que CartIcon refleja ambos productos
        Livewire::test(CartIcon::class)
            ->assertSet('count', 3) // 2 + 1
            ->assertSet('total', 139.97); // 99.98 + 39.99

        // 7. Verificar que CartPage muestra todos los productos
        Livewire::test(CartPage::class)
            ->assertSet('count', 3)
            ->assertSet('total', 139.97)
            ->assertSee('Curso de Laravel')
            ->assertSee('Curso de Vue.js');

        // 8. Actualizar cantidad desde CartPage
        Livewire::test(CartPage::class)
            ->call('updateQuantity', $product1->id, 1) // Reducir de 2 a 1
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // 9. Verificar que los totales se actualizaron
        Livewire::test(CartIcon::class)
            ->assertSet('count', 2) // 1 + 1
            ->assertSet('total', 89.98); // 49.99 + 39.99

        // 10. Remover un producto desde CartPage
        Livewire::test(CartPage::class)
            ->call('removeFromCart', $product2->id)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // 11. Verificar que solo queda un producto
        Livewire::test(CartIcon::class)
            ->assertSet('count', 1)
            ->assertSet('total', 49.99);

        // 12. Vaciar el carrito completamente
        Livewire::test(CartPage::class)
            ->call('clearCart')
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // 13. Verificar que el carrito está vacío
        Livewire::test(CartIcon::class)
            ->assertSet('count', 0)
            ->assertSet('total', 0);

        Livewire::test(CartPage::class)
            ->assertSet('count', 0)
            ->assertSet('total', 0)
            ->assertSee('Tu carrito está vacío');
    }

    public function test_cart_persists_across_components(): void
    {
        // Crear un producto
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 25.00,
            'is_active' => true,
        ]);

        // Añadir desde ProductCard
        Livewire::test(ProductCard::class, ['product' => $product])
            ->call('addToCart', 3);

        // Verificar en CartIcon
        Livewire::test(CartIcon::class)
            ->assertSet('count', 3)
            ->assertSet('total', 75.00);

        // Verificar en CartPage
        Livewire::test(CartPage::class)
            ->assertSet('count', 3)
            ->assertSet('total', 75.00)
            ->assertSee('Test Product');

        // Modificar desde CartPage
        Livewire::test(CartPage::class)
            ->call('updateQuantity', $product->id, 5);

        // Verificar que se refleja en CartIcon
        Livewire::test(CartIcon::class)
            ->assertSet('count', 5)
            ->assertSet('total', 125.00);
    }

    public function test_cart_handles_inactive_products(): void
    {
        // Crear un producto activo
        $activeProduct = Product::factory()->create([
            'name' => 'Active Product',
            'price' => 20.00,
            'is_active' => true,
        ]);

        // Crear un producto inactivo
        $inactiveProduct = Product::factory()->create([
            'name' => 'Inactive Product',
            'price' => 30.00,
            'is_active' => false,
        ]);

        // Añadir producto activo - debe funcionar
        Livewire::test(ProductCard::class, ['product' => $activeProduct])
            ->call('addToCart', 1)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');

        // Intentar añadir producto inactivo - no debe añadirse
        Livewire::test(ProductCard::class, ['product' => $inactiveProduct])
            ->call('addToCart', 1)
            ->assertHasNoErrors(); // No debe generar errores, pero tampoco añadir

        // Verificar que solo se añadió el producto activo
        Livewire::test(CartIcon::class)
            ->assertSet('count', 1)
            ->assertSet('total', 20.00);
    }
}
