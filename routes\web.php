<?php

use App\Models\Page;
use App\Models\User;
use Livewire\Livewire;
use Illuminate\Http\Request;
use App\Livewire\Settings\Profile;
use App\Livewire\Settings\Password;
use Illuminate\Support\Facades\Auth;
use App\Livewire\Settings\Appearance;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\FileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\DashboardController;
use LBCDev\OAuthManager\Http\Controllers\OAuthController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;


Route::post('/set-language', [LanguageController::class, 'set'])->name('language.set');

Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'middleware' => ['localeSessionRedirect', 'localizationRedirect', 'localeViewPath']
], function () {
    Livewire::setUpdateRoute(function ($handle) {
        return Route::post('/livewire/update', $handle);
    });

    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Rutas para productos
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');

    // Shopping Cart Routes
    Route::prefix('cart')
        ->name('cart.')
        ->group(function () {
            Route::get('/', [App\Http\Controllers\CartController::class, 'index'])->name('index');
            Route::post('/add', [App\Http\Controllers\CartController::class, 'add'])->name('add');
            Route::put('/update', [App\Http\Controllers\CartController::class, 'update'])->name('update');
            Route::delete('/remove', [App\Http\Controllers\CartController::class, 'remove'])->name('remove');
            Route::delete('/clear', [App\Http\Controllers\CartController::class, 'clear'])->name('clear');
            Route::get('/count', [App\Http\Controllers\CartController::class, 'count'])->name('count');
            Route::get('/summary', [App\Http\Controllers\CartController::class, 'summary'])->name('summary');
            Route::get('/view', function () {
                return view('cart.index');
            })->name('view');
        });

    Route::/*middleware(['auth'])
        ->*/prefix('checkout')
        ->name('checkout.')
        ->group(function () {
            Route::get('/', [App\Http\Controllers\CheckoutController::class, 'index'])->name('index');
            Route::get('/success/{order}', [App\Http\Controllers\CheckoutController::class, 'success'])->name('success');
            Route::get('/cancel', [App\Http\Controllers\CheckoutController::class, 'cancel'])->name('cancel');
        });
});


// Dashboard con redirección por rol
Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});



// Checkout Routes (require authentication)
// Route::middleware(['auth'])
//     ->prefix('checkout')
//     ->name('checkout.')
//     ->group(function () {
//         Route::get('/', [App\Http\Controllers\CheckoutController::class, 'index'])->name('index');
//         Route::post('/process', [App\Http\Controllers\CheckoutController::class, 'process'])->name('process');
//         Route::get('/success/{order}', [App\Http\Controllers\CheckoutController::class, 'success'])->name('success');
//         Route::get('/cancel', [App\Http\Controllers\CheckoutController::class, 'cancel'])->name('cancel');
//         Route::get('/summary', [App\Http\Controllers\CheckoutController::class, 'summary'])->name('summary');
//     });

// Payment Routes
Route::prefix('payment')
    ->name('payment.')
    ->group(function () {
        // Public routes for callbacks and webhooks
        Route::get('/success', [App\Http\Controllers\PaymentController::class, 'success'])->name('success');
        Route::get('/cancel', [App\Http\Controllers\PaymentController::class, 'cancel'])->name('cancel');
        Route::post('/notify', [App\Http\Controllers\PaymentController::class, 'notify'])->name('notify');
        Route::any('/webhook', [App\Http\Controllers\PaymentController::class, 'notify'])->name('webhook');

        // Authenticated routes
        Route::middleware(['auth'])->group(function () {
            Route::get('/status', [App\Http\Controllers\PaymentController::class, 'status'])->name('status');
        });
    });

Route::prefix('files')
    ->name('files.')
    ->group(function () {
        Route::get('/', [FileController::class, 'index'])->name('index');
        Route::get('/file/{file}/download', [FileController::class, 'download'])->name('download');
    });

// Rutas solo para admins
Route::middleware(['auth', 'admin'])->group(function () {
    // Route::get('/file-explorer', [OAuthController::class, 'index'])->name('file-explorer');
});

// Include auth routes before catch-all route
require __DIR__ . '/auth.php';

// Catch-all route for pages (must be last)
Route::get('/{slug}', [PageController::class, 'show']);
