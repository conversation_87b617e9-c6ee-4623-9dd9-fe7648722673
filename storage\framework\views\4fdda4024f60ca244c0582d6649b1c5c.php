<div>

    <!--[if BLOCK]><![endif]--><?php if(empty($cart)): ?>

        <!-- Empty Cart -->

        <div class="text-center py-12">

            <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>

            </svg>

            <h3 class="text-lg font-medium text-gray-900 mb-2">Tu carrito está vacío</h3>

            <p class="text-gray-600 mb-6">Parece que no has añadido ningún producto a tu carrito todavía.</p>

            <a href="<?php echo e(route('products.index')); ?>" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">

                Continuar Comprando

            </a>

        </div>

    <?php else: ?>

        <!-- Checkout Content -->

        <div class="space-y-8">

            <!-- Authentication Check -->

            <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->guest()): ?>

                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">

                    <p class="mb-4">Debes iniciar sesión para proceder con la compra.</p>

                    <div class="flex space-x-4">

                        <a href="<?php echo e(route('login')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors duration-200">

                            Iniciar Sesión

                        </a>

                        <a href="<?php echo e(route('cart.view')); ?>" class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors duration-200">

                            Volver al Carrito

                        </a>

                    </div>

                </div>

            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

           

            <!-- Cart Summary -->

            <div>

                <h2 class="text-lg font-semibold mb-4">Resumen del Pedido</h2>

                <div class="border rounded-lg p-4 bg-gray-50">

                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cart; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                        <div class="flex justify-between items-center py-2 border-b last:border-b-0">

                            <div>

                                <h3 class="font-medium"><?php echo e($item['nombre']); ?></h3>

                                <p class="text-sm text-gray-600">Cantidad: <?php echo e($item['quantity']); ?></p>

                            </div>

                            <div class="text-right">

                                <p class="font-medium">€<?php echo e(number_format($item['subtotal'], 2)); ?></p>

                                <p class="text-sm text-gray-500">€<?php echo e(number_format($item['precio'], 2)); ?> c/u</p>

                            </div>

                        </div>

                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                    <div class="pt-4 border-t">

                        <div class="flex justify-between items-center">

                            <span class="text-lg font-bold">Total:</span>

                            <span class="text-lg font-bold">€<?php echo e(number_format($total, 2)); ?></span>

                        </div>

                    </div>

                </div>

            </div>



            <!-- Error Message -->

            <!--[if BLOCK]><![endif]--><?php if($errorMessage): ?>

                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">

                    <?php echo e($errorMessage); ?>


                </div>

            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->



            <!-- Payment Gateway Selection - Only for Authenticated Users -->

            <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>

                <!--[if BLOCK]><![endif]--><?php if(!empty($availableGateways)): ?>

                    <div>

                        <h2 class="text-lg font-semibold mb-4">Método de Pago</h2>

                        <div class="space-y-4 mb-6">

                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableGateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gatewayKey => $gateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 <?php echo e($selectedGateway === $gatewayKey ? 'border-blue-500 bg-blue-50' : ''); ?>">

                                    <input

                                        type="radio"

                                        wire:model.live="selectedGateway"

                                        value="<?php echo e($gatewayKey); ?>"

                                        class="mr-3"

                                    >

                                    <span class="font-medium"><?php echo e(ucfirst($gatewayKey)); ?></span>

                                </label>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        </div>

                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['selectedGateway'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>

                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>

                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

                    </div>



                    <!-- Terms and Conditions - Only for Authenticated Users -->

                    <div>

                        <label class="flex items-start">

                            <input

                                type="checkbox"

                                wire:model.live="acceptedTerms"

                                class="mr-2 mt-1"

                            >

                            <span class="text-sm">

                                Acepto los

                                <a href="#" class="text-blue-600 hover:underline">términos y condiciones</a>

                                y la

                                <a href="#" class="text-blue-600 hover:underline">política de privacidad</a>

                            </span>

                        </label>

                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['acceptedTerms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>

                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>

                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

                    </div>



                    <!-- Action Buttons - Only for Authenticated Users -->

                    <div class="flex space-x-4">

                        <button

                            wire:click="processCheckout"

                            wire:loading.attr="disabled"

                            wire:loading.class="opacity-50 cursor-not-allowed"

                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"

                            <?php echo e($isProcessing ? 'disabled' : ''); ?>


                        >

                            <span wire:loading.remove wire:target="processCheckout">

                                Procesar Pago

                            </span>

                            <span wire:loading wire:target="processCheckout" class="flex items-center">

                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">

                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>

                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>

                                </svg>

                                Procesando...

                            </span>

                        </button>

                       

                        <a href="<?php echo e(route('cart.view')); ?>" class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors duration-200">

                            Volver al Carrito

                        </a>

                    </div>

                <?php else: ?>

                    <!-- No Payment Gateways Available - Only for Authenticated Users -->

                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">

                        <p>No hay métodos de pago disponibles en este momento. Por favor, contacta con el soporte.</p>

                    </div>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        </div>

    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

</div><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/checkout-page.blade.php ENDPATH**/ ?>