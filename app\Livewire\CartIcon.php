<?php

namespace App\Livewire;

use Livewire\Component;
use LBCDev\Ecommerce\Services\CartService;

class CartIcon extends Component
{
    public $count = 0;
    public $total = 0;

    protected $listeners = ['cartUpdated' => 'loadCart'];
    protected CartService $cartService;

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount()
    {
        $this->loadCart();
    }

    public function loadCart()
    {
        try {
            $cart = $this->cartService->getCart();
            $this->calculateTotals($cart);
        } catch (\Exception) {
            $this->count = 0;
            $this->total = 0;
        }
    }

    private function calculateTotals($cart)
    {
        $this->count = 0;
        $this->total = 0;

        foreach ($cart->getItems() as $item) {
            $this->count += $item->getQuantity();
            $this->total += $item->getSubtotal();
        }

        $this->total = round($this->total, 2);
    }

    public function render()
    {
        return view('livewire.cart-icon');
    }
}
