<div>

    @if(empty($cart))

        <!-- Empty Cart -->

        <div class="text-center py-12">

            <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>

            </svg>

            <h3 class="text-lg font-medium text-gray-900 mb-2">Tu carrito está vacío</h3>

            <p class="text-gray-600 mb-6">Parece que no has añadido ningún producto a tu carrito todavía.</p>

            <a href="{{ route('products.index') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">

                Continuar Comprando

            </a>

        </div>

    @else

        <!-- Checkout Content -->

        <div class="space-y-8">

            <!-- Authentication Check -->

            @guest

                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">

                    <p class="mb-4">Debes iniciar sesión para proceder con la compra.</p>

                    <div class="flex space-x-4">

                        <a href="{{ route('login') }}" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors duration-200">

                            Iniciar Sesión

                        </a>

                        <a href="{{ route('cart.view') }}" class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors duration-200">

                            Volver al Carrito

                        </a>

                    </div>

                </div>

            @endguest

           

            <!-- Cart Summary -->

            <div>

                <h2 class="text-lg font-semibold mb-4">Resumen del Pedido</h2>

                <div class="border rounded-lg p-4 bg-gray-50">

                    @foreach($cart as $item)

                        <div class="flex justify-between items-center py-2 border-b last:border-b-0">

                            <div>

                                <h3 class="font-medium">{{ $item['nombre'] }}</h3>

                                <p class="text-sm text-gray-600">Cantidad: {{ $item['quantity'] }}</p>

                            </div>

                            <div class="text-right">

                                <p class="font-medium">€{{ number_format($item['subtotal'], 2) }}</p>

                                <p class="text-sm text-gray-500">€{{ number_format($item['precio'], 2) }} c/u</p>

                            </div>

                        </div>

                    @endforeach

                    <div class="pt-4 border-t">

                        <div class="flex justify-between items-center">

                            <span class="text-lg font-bold">Total:</span>

                            <span class="text-lg font-bold">€{{ number_format($total, 2) }}</span>

                        </div>

                    </div>

                </div>

            </div>



            <!-- Error Message -->

            @if($errorMessage)

                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">

                    {{ $errorMessage }}

                </div>

            @endif



            <!-- Payment Gateway Selection - Only for Authenticated Users -->

            @auth

                @if(!empty($availableGateways))

                    <div>

                        <h2 class="text-lg font-semibold mb-4">Método de Pago</h2>

                        <div class="space-y-4 mb-6">

                            @foreach($availableGateways as $gatewayKey => $gateway)

                                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 {{ $selectedGateway === $gatewayKey ? 'border-blue-500 bg-blue-50' : '' }}">

                                    <input

                                        type="radio"

                                        wire:model.live="selectedGateway"

                                        value="{{ $gatewayKey }}"

                                        class="mr-3"

                                    >

                                    <span class="font-medium">{{ ucfirst($gatewayKey) }}</span>

                                </label>

                            @endforeach

                        </div>

                        @error('selectedGateway')

                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>

                        @enderror

                    </div>



                    <!-- Terms and Conditions - Only for Authenticated Users -->

                    <div>

                        <label class="flex items-start">

                            <input

                                type="checkbox"

                                wire:model.live="acceptedTerms"

                                class="mr-2 mt-1"

                            >

                            <span class="text-sm">

                                Acepto los

                                <a href="#" class="text-blue-600 hover:underline">términos y condiciones</a>

                                y la

                                <a href="#" class="text-blue-600 hover:underline">política de privacidad</a>

                            </span>

                        </label>

                        @error('acceptedTerms')

                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>

                        @enderror

                    </div>



                    <!-- Action Buttons - Only for Authenticated Users -->

                    <div class="flex space-x-4">

                        <button

                            wire:click="processCheckout"

                            wire:loading.attr="disabled"

                            wire:loading.class="opacity-50 cursor-not-allowed"

                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"

                            {{ $isProcessing ? 'disabled' : '' }}

                        >

                            <span wire:loading.remove wire:target="processCheckout">

                                Procesar Pago

                            </span>

                            <span wire:loading wire:target="processCheckout" class="flex items-center">

                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">

                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>

                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>

                                </svg>

                                Procesando...

                            </span>

                        </button>

                       

                        <a href="{{ route('cart.view') }}" class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors duration-200">

                            Volver al Carrito

                        </a>

                    </div>

                @else

                    <!-- No Payment Gateways Available - Only for Authenticated Users -->

                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">

                        <p>No hay métodos de pago disponibles en este momento. Por favor, contacta con el soporte.</p>

                    </div>

                @endif

            @endauth

        </div>

    @endif

</div>