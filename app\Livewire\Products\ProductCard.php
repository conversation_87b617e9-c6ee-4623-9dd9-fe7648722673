<?php

namespace App\Livewire\Products;

use Livewire\Component;
use LBCDev\Ecommerce\Models\Product;
use LBCDev\Ecommerce\Services\CartService;

class ProductCard extends Component
{
    public $productId;
    public $name;
    public $shortDescription;
    public $price;
    public $categories;
    public $tags;

    protected CartService $cartService;

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount(Product $product)
    {
        $this->productId = $product->id;
        $this->name = $product->name;
        $this->shortDescription = $product->short_description;
        $this->price = $product->price;
        $this->categories = $product->categoriesRelation->pluck('name')->toArray();
        $this->tags = $product->tagsRelation->pluck('name')->toArray();
    }

    public function addToCart($quantity = 1)
    {
        try {
            // Obtener el producto desde la base de datos
            $product = Product::findOrFail($this->productId);

            // Verificar que el producto esté activo
            if (!$product->is_active) {
                session()->flash('error', 'Este producto no está disponible.');
                return;
            }

            // Añadir al carrito usando el CartService del paquete
            $this->cartService->addItem($product, $quantity);

            // Emitir evento para actualizar otros componentes
            $this->dispatch('cartUpdated');

            // Mostrar mensaje de éxito
            session()->flash('success', 'Producto añadido al carrito correctamente.');
        } catch (\Exception) {
            session()->flash('error', 'Error al añadir el producto al carrito.');
        }
    }

    public function render()
    {
        return view('livewire.products.product-card');
    }
}
