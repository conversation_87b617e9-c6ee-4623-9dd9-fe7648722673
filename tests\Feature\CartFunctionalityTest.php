<?php

namespace Tests\Feature;

use Tests\TestCase;
use LBCDev\Ecommerce\Models\Product;
use LBCDev\Ecommerce\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CartFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ejecutar migraciones del paquete de ecommerce
        $this->artisan('migrate', ['--path' => 'vendor/lbcdev/ecommerce/database/migrations']);
    }

    public function test_can_add_product_to_cart_using_cart_service(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 29.99,
            'is_active' => true,
        ]);

        // Obtener el servicio de carrito
        $cartService = app(CartService::class);

        // Añadir producto al carrito
        $cart = $cartService->addItem($product, 2);

        // Verificar que el producto se añadió correctamente
        $this->assertCount(1, $cart->getItems());
        
        $item = $cart->getItems()[0];
        $this->assertEquals($product->id, $item->getId());
        $this->assertEquals(2, $item->getQuantity());
        $this->assertEquals(29.99, $item->getUnitPrice());
        $this->assertEquals(59.98, $item->getSubtotal());
    }

    public function test_can_update_product_quantity_in_cart(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 19.99,
            'is_active' => true,
        ]);

        // Obtener el servicio de carrito
        $cartService = app(CartService::class);

        // Añadir producto al carrito
        $cartService->addItem($product, 1);

        // Actualizar cantidad
        $cart = $cartService->updateQuantity($product, 3);

        // Verificar que la cantidad se actualizó
        $item = $cart->getItems()[0];
        $this->assertEquals(3, $item->getQuantity());
        $this->assertEquals(59.97, $item->getSubtotal());
    }

    public function test_can_remove_product_from_cart(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 15.99,
            'is_active' => true,
        ]);

        // Obtener el servicio de carrito
        $cartService = app(CartService::class);

        // Añadir producto al carrito
        $cartService->addItem($product, 1);
        $this->assertCount(1, $cartService->getCart()->getItems());

        // Remover producto del carrito
        $cart = $cartService->removeItem($product);

        // Verificar que el carrito está vacío
        $this->assertCount(0, $cart->getItems());
    }

    public function test_cart_calculates_total_correctly(): void
    {
        // Crear productos de prueba
        $product1 = Product::factory()->create(['name' => 'Product 1', 'price' => 10.00, 'is_active' => true]);
        $product2 = Product::factory()->create(['name' => 'Product 2', 'price' => 25.50, 'is_active' => true]);

        // Obtener el servicio de carrito
        $cartService = app(CartService::class);

        // Añadir productos al carrito
        $cartService->addItem($product1, 2); // 20.00
        $cartService->addItem($product2, 1); // 25.50

        $cart = $cartService->getCart();

        // Verificar totales
        $this->assertEquals(45.50, $cart->getTotal());
        $this->assertCount(2, $cart->getItems());
    }
}
