<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use LBCDev\Ecommerce\Models\Order;
use LBCDev\Ecommerce\Models\OrderItem;
use LBCDev\Ecommerce\Services\CartService;
use App\Services\PaymentService;

class CheckoutPage extends Component
{
    public $cart = [];
    public $total = 0;
    public $count = 0;
    public $selectedGateway = '';
    public $acceptedTerms = false;
    public $availableGateways = [];
    public $isProcessing = false;
    public $errorMessage = '';

    protected $listeners = ['cartUpdated' => 'loadCart'];
    protected CartService $cartService;
    protected PaymentService $paymentService;

    protected $rules = [
        'selectedGateway' => 'required|string',
        'acceptedTerms' => 'accepted',
    ];

    protected $messages = [
        'selectedGateway.required' => 'Debes seleccionar un método de pago.',
        'acceptedTerms.accepted' => 'Debes aceptar los términos y condiciones.',
    ];

    public function boot(CartService $cartService, PaymentService $paymentService)
    {
        $this->cartService = $cartService;
        $this->paymentService = $paymentService;
    }

    public function mount()
    {
        $this->loadCart();
        $this->loadAvailableGateways();
    }

    public function loadCart()
    {
        try {
            $cartModel = $this->cartService->getCart();
            $this->cart = $this->transformCartForView($cartModel);
            $this->calculateTotals();
        } catch (\Exception) {
            $this->cart = [];
            $this->total = 0;
            $this->count = 0;
        }
    }

    public function loadAvailableGateways()
    {
        try {
            $this->availableGateways = $this->paymentService->getAvailableGateways();
        } catch (\Exception) {
            $this->availableGateways = [];
        }
    }

    private function transformCartForView($cartModel): array
    {
        $cartArray = [];
        
        foreach ($cartModel->getItems() as $item) {
            $cartArray[] = [
                'id' => $item->getId(),
                'nombre' => $item->getName(),
                'precio' => $item->getUnitPrice(),
                'quantity' => $item->getQuantity(),
                'subtotal' => $item->getSubtotal(),
            ];
        }

        return $cartArray;
    }

    private function calculateTotals()
    {
        $this->count = 0;
        $this->total = 0;

        foreach ($this->cart as $item) {
            $this->count += $item['quantity'];
            $this->total += $item['subtotal'];
        }

        $this->total = round($this->total, 2);
    }

    public function processCheckout()
    {
        $this->errorMessage = '';
        
        // Validate form
        $this->validate();

        // Check if user is authenticated
        if (!Auth::check()) {
            $this->errorMessage = 'Debes iniciar sesión para proceder con la compra.';
            return;
        }

        // Check if cart is empty
        if (empty($this->cart)) {
            $this->errorMessage = 'Tu carrito está vacío.';
            return;
        }

        $this->isProcessing = true;

        try {
            DB::beginTransaction();

            // Create order
            $order = Order::create([
                'user_id' => Auth::id(),
                'total' => $this->total,
                'estado' => 'pendiente',
                'moneda' => config('payments.currency', 'EUR'),
            ]);

            // Create order items
            foreach ($this->cart as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['id'],
                    'cantidad' => $item['quantity'],
                    'precio_unitario' => $item['precio'],
                    'subtotal' => $item['subtotal'],
                ]);
            }

            // Create payment record
            $payment = $this->paymentService->createPayment($order, $this->selectedGateway);

            // Process payment with gateway
            $paymentResult = $this->paymentService->processPayment($payment);

            if (!$paymentResult['success']) {
                DB::rollBack();
                $this->errorMessage = $paymentResult['error'] ?? 'Error procesando el pago.';
                $this->isProcessing = false;
                return;
            }

            DB::commit();

            // Clear cart after successful order creation
            $this->cartService->clearCart();
            $this->dispatch('cartUpdated');

            if ($paymentResult['completed']) {
                // Payment completed immediately (rare)
                return redirect()->route('checkout.success', ['order' => $order->id]);
            } else {
                // Redirect to payment gateway
                return redirect()->away($paymentResult['redirect_url']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Checkout process failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'cart' => $this->cart,
            ]);

            $this->errorMessage = 'Error procesando tu pedido. Por favor intenta de nuevo.';
            $this->isProcessing = false;
        }
    }

    public function render()
    {
        return view('livewire.checkout-page');
    }
}
