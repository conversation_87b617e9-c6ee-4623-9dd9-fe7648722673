<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use LBCDev\Ecommerce\Models\Product;
use App\Livewire\Products\ProductCard;
use App\Livewire\CartIcon;
use App\Livewire\ShoppingCart;
use App\Livewire\CartPage;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CartLivewireTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Ejecutar migraciones del paquete de ecommerce
        $this->artisan('migrate', ['--path' => 'vendor/lbcdev/ecommerce/database/migrations']);
    }

    public function test_product_card_can_add_item_to_cart(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 29.99,
            'is_active' => true,
        ]);

        // Probar el componente ProductCard
        Livewire::test(ProductCard::class, ['product' => $product])
            ->call('addToCart', 1)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');
    }

    public function test_product_card_shows_error_for_inactive_product(): void
    {
        // Crear un producto inactivo
        $product = Product::factory()->create([
            'name' => 'Inactive Product',
            'price' => 29.99,
            'is_active' => false,
        ]);

        // Probar el componente ProductCard
        Livewire::test(ProductCard::class, ['product' => $product])
            ->call('addToCart', 1)
            ->assertHasNoErrors();
    }

    public function test_cart_icon_displays_correct_count(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 19.99,
            'is_active' => true,
        ]);

        // Añadir producto al carrito usando el servicio
        $cartService = app(\LBCDev\Ecommerce\Services\CartService::class);
        $cartService->addItem($product, 3);

        // Probar el componente CartIcon
        Livewire::test(CartIcon::class)
            ->assertSet('count', 3)
            ->assertSet('total', 59.97);
    }

    public function test_shopping_cart_can_update_quantity(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 15.99,
            'is_active' => true,
        ]);

        // Añadir producto al carrito usando el servicio
        $cartService = app(\LBCDev\Ecommerce\Services\CartService::class);
        $cartService->addItem($product, 1);

        // Probar el componente ShoppingCart
        Livewire::test(ShoppingCart::class)
            ->call('updateQuantity', $product->id, 5)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');
    }

    public function test_shopping_cart_can_remove_item(): void
    {
        // Crear un producto de prueba
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 12.99,
            'is_active' => true,
        ]);

        // Añadir producto al carrito usando el servicio
        $cartService = app(\LBCDev\Ecommerce\Services\CartService::class);
        $cartService->addItem($product, 2);

        // Probar el componente ShoppingCart
        Livewire::test(ShoppingCart::class)
            ->call('removeFromCart', $product->id)
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');
    }

    public function test_shopping_cart_can_clear_cart(): void
    {
        // Crear productos de prueba
        $product1 = Product::factory()->create(['name' => 'Product 1', 'price' => 10.00, 'is_active' => true]);
        $product2 = Product::factory()->create(['name' => 'Product 2', 'price' => 20.00, 'is_active' => true]);

        // Añadir productos al carrito usando el servicio
        $cartService = app(\LBCDev\Ecommerce\Services\CartService::class);
        $cartService->addItem($product1, 1);
        $cartService->addItem($product2, 1);

        // Probar el componente ShoppingCart
        Livewire::test(ShoppingCart::class)
            ->call('clearCart')
            ->assertHasNoErrors()
            ->assertDispatched('cartUpdated');
    }

    public function test_cart_page_displays_cart_items(): void
    {
        // Crear productos de prueba
        $product1 = Product::factory()->create(['name' => 'Product 1', 'price' => 15.99, 'is_active' => true]);
        $product2 = Product::factory()->create(['name' => 'Product 2', 'price' => 25.50, 'is_active' => true]);

        // Añadir productos al carrito usando el servicio
        $cartService = app(\LBCDev\Ecommerce\Services\CartService::class);
        $cartService->addItem($product1, 2);
        $cartService->addItem($product2, 1);

        // Probar el componente CartPage
        Livewire::test(CartPage::class)
            ->assertSet('count', 3)
            ->assertSet('total', 57.48) // (15.99 * 2) + (25.50 * 1)
            ->assertSee('Product 1')
            ->assertSee('Product 2');
    }

    public function test_cart_page_shows_empty_state(): void
    {
        // Probar el componente CartPage sin productos
        Livewire::test(CartPage::class)
            ->assertSet('count', 0)
            ->assertSet('total', 0)
            ->assertSee('Tu carrito está vacío');
    }
}
